package redpacket

import (
	"context"
	"fmt"
	"strconv"

	"telegram-bot-api/internal/constants"
	localConsts "telegram-bot-api/internal/consts"
	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/service"
	"telegram-bot-api/internal/utils"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/a19ba14d/tg-bot-common/consts"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
	"github.com/yalks/wallet"
	walletConstants "github.com/yalks/wallet/constants"
)

// handleConfirmPayment handles the "确定支付" button click.
// It directly creates the red packet and deducts payment.
func handleConfirmPayment(ctx context.Context, cq *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// 1. Get user state
	userState, err := service.UserState().GetUserStateByTelegramId(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "handleConfirmPayment: Error getting user state for user %d: %v", userID, err)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#SystemError}")), err
	}
	if userState == nil || userState.State != localConsts.UserStateWaitingRedPacketPaymentConfirm {
		g.Log().Warningf(ctx, "handleConfirmPayment: Invalid state for user %d. Expected %s, got %v", userID, localConsts.UserStateWaitingRedPacketPaymentConfirm, userState)
		_ = service.UserState().ClearUserStateByTelegramId(ctx, userID)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#InvalidOperationOrExpired}")), nil
	}

	// 2. Extract context data
	rpContext := userState.Context
	requiredKeys := []string{consts.RpContextKeyTokenSymbol, consts.RpContextKeyType, consts.RpContextKeyQuantity, consts.RpContextKeyAmount}
	for _, key := range requiredKeys {
		if _, ok := rpContext[key]; !ok {
			g.Log().Errorf(ctx, "handleConfirmPayment: Missing context key %s for user %d", key, userID)
			_ = service.UserState().ClearUserStateByTelegramId(ctx, userID)
			return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#SystemError}")), nil
		}
	}

	tokenSymbol := rpContext[consts.RpContextKeyTokenSymbol]
	amountStr := rpContext[consts.RpContextKeyAmount]

	// 3. Parse amount

	amount, err := decimal.NewFromString(amountStr)
	if err != nil {
		g.Log().Errorf(ctx, "handleConfirmPayment: Invalid amount %s for user %d: %v", amountStr, userID, err)
		_ = service.UserState().ClearUserStateByTelegramId(ctx, userID)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#SystemError}")), nil
	}

	// 4. Get user from database
	user, err := service.User().GetUserByTelegramId(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "handleConfirmPayment: Failed to get user %d: %v", userID, err)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#SystemError}")), err
	}
	if user == nil {
		g.Log().Warningf(ctx, "handleConfirmPayment: User not found for telegram ID %d", userID)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#UserNotFound}")), nil
	}

	// 5. Get token info
	token, err := service.Token().GetTokenBySymbol(ctx, tokenSymbol)
	if err != nil {
		g.Log().Errorf(ctx, "handleConfirmPayment: Failed to get token info for %s: %v", tokenSymbol, err)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#SystemError}")), nil
	}
	if token == nil {
		g.Log().Errorf(ctx, "handleConfirmPayment: Token %s not found", tokenSymbol)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#SystemError}")), nil
	}

	// 6. Get red packet ID from user state
	state, err := service.UserState().GetUserStateByTelegramId(ctx, userID)
	if err != nil || state == nil || state.Context == nil {
		g.Log().Errorf(ctx, "handleConfirmPayment: Failed to get user state for user %d: %v", userID, err)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#ErrorStateExpiredOrInvalid}")), nil
	}

	redPacketIDStr, ok := state.Context["red_packet_id"]
	if !ok || redPacketIDStr == "" {
		g.Log().Errorf(ctx, "handleConfirmPayment: No red packet ID found in user state for user %d", userID)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	redPacketID, err := strconv.ParseInt(redPacketIDStr, 10, 64)
	if err != nil {
		g.Log().Errorf(ctx, "handleConfirmPayment: Invalid red packet ID format %s for user %d: %v", redPacketIDStr, userID, err)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// 7. Process payment and update red packet status
	g.Log().Infof(ctx, "handleConfirmPayment: Processing payment for red packet %d, user %d", redPacketID, userID)
	result, err := processRedPacketPayment(ctx, redPacketID, userID, amount, token)
	if err != nil {
		g.Log().Errorf(ctx, "handleConfirmPayment: Failed to process payment for red packet %d, user %d: %v", redPacketID, userID, err)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#CreateRedPacketFailed}")), nil
	}

	// 8. Clear user state
	_ = service.UserState().ClearUserStateByTelegramId(ctx, userID)

	// 9. Build red packet details page
	detailsText := BuildRedPacketDetailsText(ctx, result)
	detailsKeyboard := BuildRedPacketDetailsKeyboard(ctx, result.Uuid)

	// Show success alert and edit message
	resp := &callback.EditMessageResponse{
		CallbackQueryID:   cq.ID,
		ChatID:            chatID,
		MessageID:         cq.Message.MessageID,
		Text:              detailsText,
		InlineKeyboard:    &detailsKeyboard,
		ParseMode:         "HTML",
		CallbackQueryText: i18n.Tf(ctx, "🎉 红包创建成功！"),
		ShowAlert:         true,
	}

	return resp, nil
}

// processRedPacketPayment processes payment for an existing unpaid red packet
func processRedPacketPayment(ctx context.Context, redPacketID, userID int64, amount decimal.Decimal, token *entity.Tokens) (*entity.RedPackets, error) {
	// Get user information
	user, err := service.User().GetUserByTelegramId(ctx, userID)
	if err != nil || user == nil {
		return nil, gerror.Wrapf(err, "failed to get user for telegram ID %d", userID)
	}

	// Get the red packet record
	var redPacket *entity.RedPackets
	err = dao.RedPackets.Ctx(ctx).
		Where("red_packet_id", redPacketID).
		Where("creator_user_id", userID).
		Where("is_pay", 0). // Only unpaid red packets
		Scan(&redPacket)

	if err != nil {
		return nil, gerror.Wrapf(err, "failed to get red packet %d for user %d", redPacketID, userID)
	}
	if redPacket == nil {
		return nil, gerror.Newf("red packet %d not found or already paid for user %d", redPacketID, userID)
	}

	// Process payment using wallet module
	descriptor := utils.NewFundOperationDescriptor("zh")
	req := &walletConstants.FundOperationRequest{
		UserID:      uint64(user.Id),
		TokenSymbol: token.Symbol,
		Amount:      amount,
		BusinessID:  descriptor.GenerateBusinessID(constants.FundOpRedPacketCreate, redPacket.Uuid, gtime.Now().Unix()),
		FundType:    walletConstants.FundTypeRedPacketCreate,
		Description: descriptor.FormatBasicDescription(constants.FundOpRedPacketCreate, amount.String(), token.Symbol),
		Metadata: map[string]string{
			"type":            "red_packet",
			"red_packet_id":   gconv.String(redPacketID),
			"red_packet_uuid": redPacket.Uuid,
			"quantity":        gconv.String(redPacket.Quantity),
			"rp_type":         redPacket.Type,
			"operation":       "payment",
		},
		RequestSource: "telegram",
	}

	// Process payment in a transaction
	var financialResult *wallet.FundOperationResult
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var txErr error
		financialResult, txErr = wallet.Manager().ProcessFundOperationInTx(ctx, tx, req)
		if txErr != nil {
			return txErr
		}

		// Get user's latest paid red packet to copy group_id and group_invitation_link
		var latestPaidRedPacket *entity.RedPackets
		txErr = dao.RedPackets.Ctx(ctx).TX(tx).
			Where("creator_user_id", userID).
			Where("is_pay", 1).                       // Only paid red packets
			Where("red_packet_id != ?", redPacketID). // Exclude current red packet
			OrderDesc("created_at").                  // Order by creation time descending
			Limit(1).
			Scan(&latestPaidRedPacket)
		if txErr != nil {
			g.Log().Warningf(ctx, "processRedPacketPayment: Failed to get latest paid red packet for user %d: %v", userID, txErr)
			// Don't fail the transaction, just continue without copying group info
		}

		// Prepare update data
		updateData := g.Map{
			"is_pay":         1, // Mark as paid
			"transaction_id": financialResult.TransactionID,
		}

		// If we found a previous paid red packet, copy only group information (NOT betting conditions)
		if latestPaidRedPacket != nil {
			// Copy group information if available
			if latestPaidRedPacket.GroupId != "" {
				updateData["group_id"] = latestPaidRedPacket.GroupId
				updateData["group_invitation_link"] = latestPaidRedPacket.GroupInvitationLink
				updateData["specify_group"] = latestPaidRedPacket.SpecifyGroup
			}
			// 注意：不再复制流水条件（betting_volume、betting_volume_days、specify_betting）
			// 这些条件应该由用户在创建红包时明确设置，而不是从历史红包自动复制
			g.Log().Infof(ctx, "processRedPacketPayment: Copying only group settings from red packet %d to %d: group_id=%s, specify_group=%d (betting conditions NOT copied)",
				latestPaidRedPacket.RedPacketId, redPacketID, latestPaidRedPacket.GroupId, latestPaidRedPacket.SpecifyGroup)
		}

		// Update red packet status to paid and set transaction ID within the same transaction
		_, txErr = dao.RedPackets.Ctx(ctx).TX(tx).
			Where("red_packet_id", redPacketID).
			Data(updateData).
			Update()
		if txErr != nil {
			return txErr
		}

		// Pre-generate red packet claims
		// Create a service input to reuse existing logic
		createInput := service.CreateRedPacketInput{
			UserId:            int64(user.Id),
			CreatorUserId:     userID,
			CreatorUsername:   redPacket.CreatorUsername,
			TokenId:           uint(redPacket.TokenId),
			TokenSymbol:       redPacket.Symbol,
			Type:              redPacket.Type,
			Quantity:          redPacket.Quantity,
			TotalAmount:       redPacket.TotalAmount,
			Blessing:          redPacket.Memo,
			RedPacketImagesId: redPacket.RedPacketImagesId,
			CoverFileID:       redPacket.CoverFileId,
			ThumbUrl:          redPacket.ThumbUrl,
			MessageId:         gconv.Int(redPacket.MessageId),
		}

		// Create red packet claims using the existing logic
		err := service.RedPacket().CreateRedPacketClaims(ctx, tx, redPacket, createInput, user.TenantId, int64(user.Id))
		if err != nil {
			g.Log().Errorf(ctx, "processRedPacketPayment: Failed to create red packet claims for red packet %d: %v", redPacketID, err)
			return gerror.Wrapf(err, "failed to create red packet claims")
		}

		return nil
	})

	if err != nil {
		g.Log().Errorf(ctx, "processRedPacketPayment: Payment transaction failed for red packet %d, user %d: %v", redPacketID, userID, err)
		return nil, gerror.Wrapf(err, "payment failed for red packet %d", redPacketID)
	}

	// Get updated red packet record
	err = dao.RedPackets.Ctx(ctx).
		Where("red_packet_id", redPacketID).
		Scan(&redPacket)

	if err != nil {
		return nil, gerror.Wrapf(err, "failed to get updated red packet %d", redPacketID)
	}

	g.Log().Infof(ctx, "processRedPacketPayment: Successfully processed payment for red packet %d, user %d, transaction %s",
		redPacketID, userID, financialResult.TransactionID)

	return redPacket, nil
}

// handleSetCover handles the "设置封面" button click.
// It transitions to cover selection flow.
func handleSetCover(ctx context.Context, cq *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// 1. Get user state
	userState, err := service.UserState().GetUserStateByTelegramId(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "handleSetCover: Error getting user state for user %d: %v", userID, err)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#SystemError}")), err
	}
	if userState == nil || userState.State != localConsts.UserStateWaitingRedPacketPaymentConfirm {
		g.Log().Warningf(ctx, "handleSetCover: Invalid state for user %d. Expected %s, got %v", userID, localConsts.UserStateWaitingRedPacketPaymentConfirm, userState)
		_ = service.UserState().ClearUserStateByTelegramId(ctx, userID)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#InvalidOperationOrExpired}")), nil
	}

	// 2. Transition to cover selection state
	newState := model.NewUserState(consts.UserStateWaitingRedPacketCoverSelection, userState.Context, "", 0, 600) // 10 minutes TTL
	newState.MessageIDToEdit = 0                                                                                  // Clear message ID to edit, as we will delete and send new
	errSet := service.UserState().SetUserStateByTelegramId(ctx, userID, newState)
	if errSet != nil {
		g.Log().Errorf(ctx, "handleSetCover: Failed to update user state to cover selection for user %d: %v", userID, errSet)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#SetUserStateError}")), nil
	}

	g.Log().Infof(ctx, "handleSetCover: User %d transitioning to cover selection", userID)

	// 3. Send cover selection message
	defaultCoverFileId := g.Cfg().MustGet(ctx, "telegram.redPacketDefaultCoverFileId").String()
	if defaultCoverFileId == "" {
		g.Log().Error(ctx, "handleSetCover: Missing telegram.redPacketDefaultCoverFileId in config")
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#SystemErrorConfigMissing}")), nil
	}

	keyboard := GetCoverSelectionKeyboard(ctx)
	caption := i18n.T(ctx, "{#ConfirmCoverTitle}")

	photoConfig := tgbotapi.NewPhoto(chatID, tgbotapi.FileID(defaultCoverFileId))
	photoConfig.Caption = caption
	photoConfig.ReplyMarkup = keyboard

	// Return response to delete the old message and send the new photo message
	return &callback.DeleteAndSendPhotoResponse{
		CallbackQueryID:   cq.ID,
		ChatID:            chatID,
		MessageIDToDelete: messageID,
		PhotoConfig:       photoConfig,
	}, nil
}

// handleBackToAmount handles the "返回" button click.
// It returns to amount input step.
func handleBackToAmount(ctx context.Context, cq *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// 1. Get user state
	userState, err := service.UserState().GetUserStateByTelegramId(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "handleBackToAmount: Error getting user state for user %d: %v", userID, err)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#SystemError}")), err
	}
	if userState == nil || userState.State != localConsts.UserStateWaitingRedPacketPaymentConfirm {
		g.Log().Warningf(ctx, "handleBackToAmount: Invalid state for user %d. Expected %s, got %v", userID, localConsts.UserStateWaitingRedPacketPaymentConfirm, userState)
		_ = service.UserState().ClearUserStateByTelegramId(ctx, userID)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#InvalidOperationOrExpired}")), nil
	}

	// 2. Extract context data
	rpContext := userState.Context
	tokenSymbol := rpContext[consts.RpContextKeyTokenSymbol]
	rpType := rpContext[consts.RpContextKeyType]
	quantityStr := rpContext[consts.RpContextKeyQuantity]

	// 3. Remove amount from context and transition back to amount input state
	delete(rpContext, consts.RpContextKeyAmount)
	newState := model.NewUserState(consts.UserStateWaitingRedPacketAmount, rpContext, model.InputTypeText, 3, 300)
	newState.MessageIDToEdit = int64(messageID)
	errSet := service.UserState().SetUserStateByTelegramId(ctx, userID, newState)
	if errSet != nil {
		g.Log().Errorf(ctx, "handleBackToAmount: Failed to update user state to amount input for user %d: %v", userID, errSet)
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#SetUserStateError}")), nil
	}

	// 4. Build amount prompt text
	quantity, _ := strconv.Atoi(quantityStr)
	var promptText string
	if rpType == string(consts.RedPacketTypeRandom) {
		promptText = i18n.Tf(ctx, "{#EnterRedPacketTotalAmount}", tokenSymbol)
	} else {
		promptText = i18n.Tf(ctx, "{#EnterRedPacketSingleAmount}", tokenSymbol)
	}

	// Add quantity info
	promptText += "\n" + i18n.Tf(ctx, "数量: %d 个", quantity)

	// 5. Create keyboard with back button only
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"↩️ "+i18n.T(ctx, "{#BackButton}"),
				localConsts.CallbackRedPacketBackToType,
			),
		),
	)

	// 6. Check if current message has media (photo/cover)
	// If it has media, we need to delete and send new text message
	// If it's text only, we can edit it directly
	hasMedia := cq.Message.Photo != nil || cq.Message.Video != nil || cq.Message.Animation != nil || cq.Message.Document != nil

	if hasMedia {
		g.Log().Infof(ctx, "handleBackToAmount: Current message has media, deleting and sending new text message for user %d", userID)
		// Delete current message and send new text message
		response := callback.NewDeleteAndSendResponse(chatID, messageID, promptText, &keyboard)
		response.CallbackQueryID = cq.ID
		response.ParseMode = "HTML"
		return response, nil
	} else {
		g.Log().Infof(ctx, "handleBackToAmount: Current message is text only, editing directly for user %d", userID)
		// Edit current text message
		return callback.NewExtendedEditMessageResponse(cq.ID, chatID, messageID, promptText, &keyboard), nil
	}
}

// handleBackToMain handles the "返回" button click from red packet type selection.
// It returns to the red packet main menu.
func handleBackToMain(ctx context.Context, cq *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID

	// Clear any existing user state
	_ = service.UserState().ClearUserStateByTelegramId(ctx, userID)

	g.Log().Infof(ctx, "handleBackToMain: User %d returning to red packet main menu", userID)

	// Create red packet main menu response
	response := NewRedPacketMenuResponse(chatID, messageID)
	return response, nil
}

// handleBackToType handles the "返回" button click from red packet quantity input.
// It returns to the red packet type selection page.
func handleBackToType(ctx context.Context, cq *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Clear any existing user state
	_ = service.UserState().ClearUserStateByTelegramId(ctx, userID)

	g.Log().Infof(ctx, "handleBackToType: User %d returning to red packet type selection", userID)

	// Get default token symbol from config
	defaultTokenSymbol := g.Cfg().MustGet(ctx, "red_packet.default_token_symbol", "CNY").String()

	// Create red packet type selection keyboard with back button to return to main menu
	randomCallback := fmt.Sprintf("%s:%s", consts.CallbackSelectRedPacketTypeRandom, defaultTokenSymbol)
	fixedCallback := fmt.Sprintf("%s:%s", consts.CallbackSelectRedPacketTypeFixed, defaultTokenSymbol)

	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#RedPacketTypeRandomButton}"), randomCallback),
			tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#RedPacketTypeFixedButton}"), fixedCallback),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("↩️ "+i18n.T(ctx, "{#BackButton}"), localConsts.CallbackRedPacketBackToMain),
		),
	)

	return callback.NewExtendedEditMessageResponse(cq.ID, chatID, messageID, i18n.T(ctx, "{#SelectRedPacketTypeTitle}"), &keyboard), nil
}

// BuildRedPacketDetailsKeyboard builds the keyboard for red packet details page
func BuildRedPacketDetailsKeyboard(ctx context.Context, redPacketUUID string) tgbotapi.InlineKeyboardMarkup {
	i18n := service.I18n().Instance()

	// Get red packet record to check status
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Scan(&redPacket)

	// Build keyboard rows based on status
	var rows [][]tgbotapi.InlineKeyboardButton

	// Only show action buttons if red packet is active
	if err == nil && redPacket != nil && redPacket.Status == string(consts.RedPacketStatusActive) {
		// First row: Share button
		rows = append(rows, tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"🚀 "+i18n.T(ctx, "再发一个"),
				"rp:add",
			),
			tgbotapi.NewInlineKeyboardButtonSwitch(
				"🚀 "+i18n.T(ctx, "发送红包"),
				consts.InlineQueryPrefixShareRedPacket+redPacketUUID,
			),
		))

		// Second row: Set memo and Set conditions
		rows = append(rows, tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"📝 "+i18n.T(ctx, "设置备注"),
				fmt.Sprintf("rp:set_memo:%s", redPacketUUID),
			),
			tgbotapi.NewInlineKeyboardButtonData(
				"⚙️ "+i18n.T(ctx, "设置条件"),
				fmt.Sprintf("rp:set_conditions:%s", redPacketUUID),
			),
		))
	}

	// Always show close button
	// rows = append(rows, tgbotapi.NewInlineKeyboardRow(
	// 	tgbotapi.NewInlineKeyboardButtonData(
	// 		"❌ "+i18n.T(ctx, "关闭"),
	// 		"rp:close_details",
	// 	),
	// ))

	keyboard := tgbotapi.NewInlineKeyboardMarkup(rows...)
	return keyboard
}
