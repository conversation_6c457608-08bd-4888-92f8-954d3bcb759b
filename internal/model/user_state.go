package model

import "time"

// UserState holds the current interaction state of a user.
type UserState struct {
	State             string            `json:"state"`                         // Current state, e.g., "IDLE", "AWAITING_GOOGLE_2FA_CODE"
	Context           map[string]string `json:"context,omitempty"`             // Action-specific context data
	ExpectedInputType string            `json:"expected_input_type,omitempty"` // Expected input format, e.g., "numeric_6_digits"
	RetryCount        int               `json:"retry_count"`                   // Current retry attempt count
	MaxRetries        int               `json:"max_retries"`                   // Maximum allowed retries for this state
	MessageIDToEdit   int64             `json:"message_id_to_edit,omitempty"`  // Optional: Message ID to edit for prompts/feedback
	CreatedAt         int64             `json:"created_at"`                    // Unix timestamp when the state was created/updated
	TTL               int               `json:"ttl"`                           // Logical TTL in seconds for the state
}

// State constants define the possible interaction states a user can be in.
const (
	StateIdle                              = "IDLE"                                 // User is not in any specific interaction flow.
	StateAwaitingGoogle2FACode             = "AWAITING_GOOGLE_2FA_CODE"             // User needs to input Google 2FA code.
	StateAwaitingPaymentPassword           = "AWAITING_PAYMENT_PASSWORD"            // User needs to input payment password.
	StateWaitingBackupVerificationPassword = "waiting_backup_verification_password" // 等待备用账户输入主账户支付密码
	StateWaitingForTransferRecipient       = "WAITING_TRANSFER_RECIPIENT"           // 等待用户输入转账收款人
	StateWaitingForTransferAmount          = "WAITING_TRANSFER_AMOUNT"              // 等待用户输入转账金额
	StateWaitingForTransferConfirmation    = "WAITING_TRANSFER_CONFIRMATION"        // 等待用户确认转账（免密）
	StateWaitingForTransferPassword        = "WAITING_TRANSFER_PASSWORD"            // 等待用户输入转账支付密码

	StateAwaitingCoverUpload          = "AWAITING_COVER_UPLOAD"            // 等待用户上传红包封面图片
	StateAwaitingDailyReportDateRange = "AWAITING_DAILY_REPORT_DATE_RANGE" // 等待用户输入日充提报表日期范围

	// Add other states here as needed, e.g.:
	// StateAwaitingWithdrawAmount   = "AWAITING_WITHDRAW_AMOUNT"
	// StateAwaitingWithdrawAddress  = "AWAITING_WITHDRAW_ADDRESS"
	// StateAwaitingDepositAmount    = "AWAITING_DEPOSIT_AMOUNT"
)

// ExpectedInputType constants define the expected format or type of user input.
const (
	InputTypeNumeric6Digits = "numeric_6_digits" // Expecting a 6-digit numeric code.
	InputTypeText           = "text"             // Expecting any text input.
	InputTypeAmount         = "amount"           // Expecting a valid monetary amount.
	InputTypeCryptoAddress  = "crypto_address"   // Expecting a valid cryptocurrency address.
	InputTypePhoto          = "photo"            // Expecting a photo message.
	InputTypeCallback       = "callback"         // Expecting input from callback (e.g., keyboard buttons).
	// Add other input types here as needed.
)

// NewUserState creates a new UserState instance.
func NewUserState(state string, context map[string]string, expectedInputType string, maxRetries int, ttlSeconds int) *UserState {
	return &UserState{
		State:             state,
		Context:           context,
		ExpectedInputType: expectedInputType,
		RetryCount:        0,
		MaxRetries:        maxRetries,
		CreatedAt:         time.Now().Unix(),
		TTL:               ttlSeconds,
	}
}

// IsExpired checks if the state has logically expired based on its CreatedAt and TTL.
func (us *UserState) IsExpired() bool {
	if us.TTL <= 0 {
		return false // No expiration if TTL is not positive
	}
	return time.Now().Unix() > us.CreatedAt+int64(us.TTL)
}

// ResetTTL updates the CreatedAt timestamp, effectively resetting the TTL timer.
func (us *UserState) ResetTTL() {
	us.CreatedAt = time.Now().Unix()
}
